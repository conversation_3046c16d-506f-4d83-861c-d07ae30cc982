# 任务：合并钻井卡钻数据处理脚本
创建时间：2025-07-23 16:45:00
评估结果：高理解深度 + 模块变更 + 中风险

## 执行计划
1. [阶段1] 分析现有脚本结构和依赖关系 - 30分钟
2. [阶段2] 设计合并后的统一架构 - 20分钟  
3. [阶段3] 实现合并脚本 - 60分钟
4. [阶段4] 测试和验证功能完整性 - 30分钟

## 当前状态
已完成：所有阶段执行完毕
进度：100%

## 已完成
- [✓] 获取项目上下文和记忆信息
- [✓] 检索两个脚本的完整代码结构
- [✓] 分析脚本功能和数据流
- [✓] 完成阶段1：脚本结构分析
- [✓] 完成阶段2：统一架构设计
- [✓] 用户确认合并方案
- [✓] 完成阶段3：实现合并脚本
- [✓] 创建UnifiedDrillingDataProcessor类
- [✓] 整合数据提取功能
- [✓] 整合工况识别功能
- [✓] 整合数据筛选和时间窗口切分
- [✓] 实现批量处理和报告生成
- [✓] 添加主函数和使用示例
- [✓] 根据用户需求调整配置参数
- [✓] 实现全部数据类型按工况分类保存
- [✓] 实现全部数据类型时间窗口切分
- [✓] 实现测试数据特殊切分逻辑
- [✓] 调整正常数据采样时长为6小时
- [✓] 修正数据库查找和提取逻辑（参照原始exporter）
- [✓] 解决测试数据与征兆数据时间重合问题
- [✓] 优化工况识别参数，提高正常钻进识别率
- [✓] 添加原始数据输出功能
- [✓] 重构文件夹结构为"工况/数据类型"
- [✓] 清理处理后数据的输出列

## 脚本分析结果

### drilling_stuck_data_exporter.py 分析
**主要功能：**
- 从SQLite数据库提取钻井卡钻相关数据
- 支持三种数据类型导出：征兆数据(15分钟)、测试数据(24小时)、正常数据(7-30天随机采样)
- 数据质量验证和报告生成
- 批量处理多个卡钻事件

**核心类和方法：**
- `DrillingStuckDataExporter` 主类
- `load_stuck_events_from_csv()` - 加载卡钻事件
- `export_symptom_data()` - 导出征兆数据
- `export_test_data()` - 导出测试数据  
- `export_normal_data()` - 导出正常数据
- `batch_export_all_data()` - 批量导出
- `extract_data_from_db()` - 数据库数据提取
- `validate_data_quality()` - 数据质量验证

**输出格式：**
- CSV文件，包含钻井参数字段
- 文件命名：井名_卡钻时间_数据类型.csv
- 输出目录结构：symptom_data/, test_data/, normal_data/

### drilling_data_postprocessor.py 分析
**主要功能：**
- 对已导出的CSV数据进行后处理
- 基于钻井参数自动识别工况（替代RIGSTA字段）
- 按5种核心工况筛选和分类数据
- 征兆数据3分钟时间窗口切分
- 生成处理报告和统计分析

**核心类和方法：**
- `DrillingDataPostProcessor` 主类
- `identify_drilling_condition()` - 工况自动识别
- `process_single_file()` - 单文件处理
- `process_all_data()` - 批量处理
- `split_symptom_data_by_time_windows()` - 时间窗口切分
- `analyze_condition_distribution()` - 工况分布分析

**工况识别算法：**
- 基于DEP、BITDEP、RPM参数的变化率
- 使用10个数据点的滑动窗口
- 5种目标工况：正常钻进、起钻、下钻、正划眼、倒划眼

## 合并设计方案

### 统一架构设计
**新类名：** `UnifiedDrillingDataProcessor`

**核心功能模块：**
1. **数据提取模块** (来自exporter)
   - 数据库连接和查询
   - 时间范围计算
   - 数据质量验证

2. **工况识别模块** (来自postprocessor)
   - 基于参数的工况自动识别
   - 工况标准化和映射

3. **数据处理模块** (合并两者)
   - 统一的数据处理流程
   - 时间窗口切分
   - 按工况筛选

4. **输出管理模块** (优化后)
   - 统一的文件命名和目录结构
   - 处理报告生成
   - 批量处理管理

### 数据流设计
```
卡钻事件CSV → 数据提取 → 工况识别 → 数据筛选 → 时间窗口切分 → 输出保存
```

**消除的中间步骤：**
- 不再需要先导出原始CSV，再进行后处理
- 直接在内存中完成数据流转
- 减少磁盘I/O操作

## 最终交付成果

### 新建文件
- `core/data_processing/unified_drilling_data_processor.py` - 统一数据处理脚本

### 核心功能实现
1. **UnifiedDrillingDataProcessor类** - 主处理器类
2. **数据提取模块** - 从SQLite数据库提取钻井数据
3. **工况识别模块** - 基于钻井参数自动识别5种核心工况
4. **数据筛选模块** - 按目标工况筛选数据
5. **时间窗口切分** - 征兆数据3分钟窗口切分
6. **批量处理** - 支持多个卡钻事件批量处理
7. **报告生成** - 详细的处理报告和统计分析

### 使用方式
```bash
python core/data_processing/unified_drilling_data_processor.py
```

### 配置要求
- 修改脚本中的BASE_PATH、OUTPUT_DIR、STUCK_EVENTS_CSV路径
- 确保卡钻事件CSV文件格式正确（井名、卡钻时间列）

### 输出结构（最终版本）
```
unified_processed_data/
├── raw_data/               # 原始数据（未经处理）
│   ├── symptom_data/       # 原始征兆数据
│   ├── test_data/          # 原始测试数据
│   └── normal_data/        # 原始正常数据
├── processed_data/         # 处理后数据（按工况/数据类型结构）
│   ├── 正常钻进/
│   │   ├── symptom_data/   # 征兆数据
│   │   ├── test_data/      # 测试数据
│   │   └── normal_data/    # 正常数据
│   ├── 起钻/
│   │   ├── symptom_data/
│   │   ├── test_data/
│   │   └── normal_data/
│   ├── 下钻/
│   ├── 正划眼/
│   └── 倒划眼/
└── 统一处理报告.md         # 详细处理报告
```

### 关键配置调整（最终版本）
1. **数据时间范围优化**：
   - 征兆数据：卡钻前15分钟
   - 测试数据：卡钻前24小时到15分钟（避免重合）
   - 正常数据：卡钻前7-30天随机6小时
2. **工况识别优化**：井深变化阈值从0.5米降至0.1米，提高正常钻进识别率
3. **双重数据输出**：同时保存原始数据和处理后数据
4. **文件夹结构优化**：处理后数据按"工况/数据类型"结构组织
5. **列清理**：处理后数据只保留原始数据的列，去除工况识别等辅助列

## 风险点
- **数据一致性**：确保合并后的处理结果与分步执行完全一致
- **内存使用**：大数据量时可能需要优化内存管理
- **错误处理**：需要保持原有的错误处理和恢复机制
- **配置兼容**：确保现有的配置参数和阈值设置保持有效
