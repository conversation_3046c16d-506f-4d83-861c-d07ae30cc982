#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
钻井卡钻数据后处理器
对已导出的钻井数据进行工况自动识别、分类、时间窗口切分和质量分析

功能：
1. 基于钻井参数自动识别工况（替代原有的RIGSTA字段依赖）
2. 为所有三种数据类型添加工况判断功能
3. 按5种核心工况筛选和分类数据
4. 对征兆数据进行3分钟时间窗口切分
5. 生成详细的处理报告
"""

import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import json
import os
import shutil
import warnings
warnings.filterwarnings('ignore')

class DrillingDataPostProcessor:
    """
    钻井数据后处理器
    """
    
    def __init__(self, input_base_dir: str, output_base_dir: str):
        self.input_base_dir = Path(input_base_dir)
        self.output_base_dir = Path(output_base_dir)
        
        # 目标工况列表（只保留这5种）
        self.target_conditions = [
            '正常钻进',  # 对应原来的'钻进'
            '起钻',
            '下钻', 
            '正划眼',
            '倒划眼'
        ]
        
        # 工况映射（标准化不同的表达方式）
        self.condition_mapping = {
            '正常钻进': ['正常钻进', '钻进', '钻井', 'DRILLING', '钻', '进'],
            '起钻': ['起钻', '上提', '起下钻', 'TRIP_OUT', '起', '上'],
            '下钻': ['下钻', '下放', '下钻具', 'TRIP_IN', '下', '入'],
            '正划眼': ['正划眼', '划眼', '扩眼', 'REAMING', '划', '扩'],
            '倒划眼': ['倒划眼', '反划眼', 'BACK_REAMING', '倒', '反']
        }
        
        # 数据类型配置
        self.data_types = ['symptom_data', 'test_data', 'normal_data']
        self.data_type_names = {
            'symptom_data': '征兆数据',
            'test_data': '测试数据', 
            'normal_data': '正常数据'
        }
        
        # 时间窗口配置（仅用于征兆数据）
        self.time_windows = [
            {'name': '窗口1', 'start': 0, 'end': 3, 'description': '0-3分钟'},
            {'name': '窗口2', 'start': 3, 'end': 6, 'description': '3-6分钟'},
            {'name': '窗口3', 'start': 6, 'end': 9, 'description': '6-9分钟'},
            {'name': '窗口4', 'start': 9, 'end': 12, 'description': '9-12分钟'},
            {'name': '窗口5', 'start': 12, 'end': 15, 'description': '12-15分钟'}
        ]
        
        # 创建输出目录
        self.output_base_dir.mkdir(parents=True, exist_ok=True)
        
        # 统计信息
        self.processing_stats = {
            'total_files': 0,
            'processed_files': 0,
            'failed_files': 0,
            'condition_distribution': {},
            'time_windows_created': 0,
            'filtered_files': 0
        }
        
        # 工况识别参数配置（优化后的参数）
        self.condition_thresholds = {
            'time_window_points': 10,           # 固定数据点窗口（约10分钟）
            'depth_change_threshold': 0.5,      # 井深变化阈值(米) - 调整为更合理的0.5米
            'bitdep_change_threshold': 0.3,     # 钻头位置变化阈值(米) - 调整为0.3米
            'rpm_threshold': 10.0,              # 转速阈值(rpm) - 保持10rpm
        }

        print(f"🔧 初始化钻井数据后处理器")
        print(f"   - 输入目录: {self.input_base_dir}")
        print(f"   - 输出目录: {self.output_base_dir}")
        print(f"   - 目标工况: {', '.join(self.target_conditions)}")
        print(f"   - 工况识别: 基于钻井参数自动识别")
    
    def identify_drilling_condition(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        基于钻井参数自动识别工况

        工况判断规则:
        1. 钻进: 井深变化值 > 0
        2. 下钻: 井深变化值 ≈ 0 AND 转速 ≈ 0 AND 钻头位置变化值 > 0
        3. 起钻: 井深变化值 ≈ 0 AND 转速 ≈ 0 AND 钻头位置变化值 < 0
        4. 正划眼: 井深变化值 ≈ 0 AND 转速 > 0 AND 钻头位置变化值 > 0
        5. 倒划眼: 井深变化值 ≈ 0 AND 转速 > 0 AND 钻头位置变化值 < 0
        """
        if df.empty:
            return df

        # 确保必要字段存在
        required_fields = ['DEP', 'BITDEP', 'RPM', 'date']
        missing_fields = [field for field in required_fields if field not in df.columns]
        if missing_fields:
            print(f"⚠️ 缺少必要字段进行工况识别: {missing_fields}")
            df['identified_condition'] = '其他'
            return df

        # 转换时间字段
        df = df.copy()
        df['datetime'] = pd.to_datetime(df['date'])
        df = df.sort_values('datetime').reset_index(drop=True)

        # 计算变化率（使用固定数据点窗口）
        window_size = self.condition_thresholds['time_window_points']

        df['dep_change'] = df['DEP'].rolling(window=window_size, min_periods=1).apply(
            lambda x: x.iloc[-1] - x.iloc[0] if len(x) > 1 else 0
        )

        df['bitdep_change'] = df['BITDEP'].rolling(window=window_size, min_periods=1).apply(
            lambda x: x.iloc[-1] - x.iloc[0] if len(x) > 1 else 0
        )

        # 应用工况识别规则
        conditions = []
        for _, row in df.iterrows():
            dep_change = row['dep_change']
            bitdep_change = row['bitdep_change']
            rpm = row['RPM']

            # 处理NaN值
            if pd.isna(dep_change) or pd.isna(bitdep_change) or pd.isna(rpm):
                conditions.append('其他')
                continue

            # 应用判断规则（使用优化后的阈值）
            if dep_change > self.condition_thresholds['depth_change_threshold']:
                # 井深增加 -> 钻进
                conditions.append('正常钻进')
            elif abs(dep_change) <= self.condition_thresholds['depth_change_threshold']:
                # 井深基本不变
                if rpm <= self.condition_thresholds['rpm_threshold']:
                    # 转速低
                    if bitdep_change > self.condition_thresholds['bitdep_change_threshold']:
                        conditions.append('下钻')
                    elif bitdep_change < -self.condition_thresholds['bitdep_change_threshold']:
                        conditions.append('起钻')
                    else:
                        conditions.append('其他')
                else:
                    # 转速高
                    if bitdep_change > self.condition_thresholds['bitdep_change_threshold']:
                        conditions.append('正划眼')
                    elif bitdep_change < -self.condition_thresholds['bitdep_change_threshold']:
                        conditions.append('倒划眼')
                    else:
                        conditions.append('其他')
            else:
                conditions.append('其他')

        df['identified_condition'] = conditions
        return df

    def standardize_condition(self, condition_value) -> Optional[str]:
        """
        标准化工况字段，只返回目标工况
        """
        if pd.isna(condition_value) or condition_value == '' or condition_value is None:
            return None

        condition_str = str(condition_value).strip()

        # 精确匹配
        for target_condition, variants in self.condition_mapping.items():
            if condition_str in variants:
                return target_condition

        # 模糊匹配（包含关系）
        for target_condition, variants in self.condition_mapping.items():
            for variant in variants:
                if variant and (variant in condition_str or condition_str in variant):
                    return target_condition

        # 不在目标工况中，返回None（将被过滤）
        return None
    
    def load_csv_with_encoding(self, file_path: Path) -> Optional[pd.DataFrame]:
        """
        自动检测编码并加载CSV文件
        """
        encodings = ['utf-8', 'utf-8-sig', 'gbk', 'gb2312']
        
        for encoding in encodings:
            try:
                df = pd.read_csv(file_path, encoding=encoding)
                return df
            except UnicodeDecodeError:
                continue
            except Exception as e:
                print(f"⚠️ 读取文件失败 {file_path.name}: {e}")
                return None
        
        print(f"❌ 无法读取文件（尝试了所有编码）: {file_path.name}")
        return None
    
    def analyze_condition_distribution(self) -> Dict:
        """
        分析当前数据的工况分布情况
        """
        print(f"\n📊 分析工况分布情况...")
        
        distribution_stats = {
            'by_data_type': {},
            'overall': {},
            'file_count': 0,
            'total_records': 0
        }
        
        for data_type in self.data_types:
            data_dir = self.input_base_dir / data_type
            if not data_dir.exists():
                print(f"⚠️ 数据目录不存在: {data_dir}")
                continue
            
            type_stats = {
                'files': 0,
                'records': 0,
                'conditions': {}
            }
            
            csv_files = list(data_dir.glob("*.csv"))
            print(f"   📁 {self.data_type_names[data_type]}: 发现 {len(csv_files)} 个文件")
            
            for csv_file in csv_files:
                df = self.load_csv_with_encoding(csv_file)
                if df is None or df.empty:
                    continue
                
                type_stats['files'] += 1
                type_stats['records'] += len(df)
                
                # 统计工况分布（使用自动识别）
                df_with_conditions = self.identify_drilling_condition(df)
                if 'identified_condition' in df_with_conditions.columns:
                    condition_counts = df_with_conditions['identified_condition'].value_counts()
                    for condition, count in condition_counts.items():
                        if condition not in type_stats['conditions']:
                            type_stats['conditions'][condition] = 0
                        type_stats['conditions'][condition] += count
            
            distribution_stats['by_data_type'][data_type] = type_stats
            distribution_stats['file_count'] += type_stats['files']
            distribution_stats['total_records'] += type_stats['records']
            
            # 合并到总体统计
            for condition, count in type_stats['conditions'].items():
                if condition not in distribution_stats['overall']:
                    distribution_stats['overall'][condition] = 0
                distribution_stats['overall'][condition] += count
        
        print(f"   📈 总计: {distribution_stats['file_count']} 个文件, {distribution_stats['total_records']} 条记录")
        
        return distribution_stats
    
    def filter_and_classify_by_condition(self, df: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """
        按工况筛选和分类数据（使用自动识别的工况）
        """
        classified_data = {}

        # 使用新的工况自动识别
        df_with_conditions = self.identify_drilling_condition(df)

        if 'identified_condition' not in df_with_conditions.columns:
            print("⚠️ 工况识别失败")
            return classified_data

        # 按目标工况分类
        for condition in self.target_conditions:
            condition_data = df_with_conditions[df_with_conditions['identified_condition'] == condition].copy()
            if not condition_data.empty:
                # 保留识别的工况字段用于后续分析
                classified_data[condition] = condition_data
                print(f"      📊 {condition}: {len(condition_data)} 条记录")

        return classified_data
    
    def create_time_windows(self, df: pd.DataFrame, base_filename: str) -> List[Dict]:
        """
        创建3分钟时间窗口（仅用于征兆数据）
        """
        if 'date' not in df.columns:
            print("⚠️ 数据中没有date字段，无法创建时间窗口")
            return []
        
        # 确保时间字段是datetime类型
        df['date'] = pd.to_datetime(df['date'])
        df = df.sort_values('date').reset_index(drop=True)
        
        # 计算数据的时间范围
        start_time = df['date'].min()
        end_time = df['date'].max()
        total_minutes = (end_time - start_time).total_seconds() / 60
        
        if total_minutes < 14:  # 少于14分钟的数据可能不完整
            print(f"⚠️ 数据时间范围过短: {total_minutes:.1f}分钟")
        
        windows_data = []
        
        for window in self.time_windows:
            window_start = start_time + timedelta(minutes=window['start'])
            window_end = start_time + timedelta(minutes=window['end'])
            
            # 筛选时间窗口内的数据
            window_data = df[
                (df['date'] >= window_start) & 
                (df['date'] < window_end)
            ].copy()
            
            if not window_data.empty:
                # 生成窗口文件名
                window_filename = base_filename.replace('.csv', f'_{window["name"]}.csv')
                
                windows_data.append({
                    'window_name': window['name'],
                    'description': window['description'],
                    'filename': window_filename,
                    'data': window_data,
                    'record_count': len(window_data),
                    'time_range': f"{window_start.strftime('%H:%M:%S')} - {window_end.strftime('%H:%M:%S')}"
                })
        
        return windows_data

    def create_time_windows_for_normal_data(self, df: pd.DataFrame, base_filename: str) -> List[Dict]:
        """
        为正常数据创建3分钟时间窗口（24小时数据切分为多个3分钟窗口）
        """
        if 'date' not in df.columns:
            print("⚠️ 数据中没有date字段，无法创建时间窗口")
            return []

        # 确保时间字段是datetime类型
        df['date'] = pd.to_datetime(df['date'])
        df = df.sort_values('date').reset_index(drop=True)

        # 计算数据的时间范围
        start_time = df['date'].min()
        end_time = df['date'].max()
        total_minutes = (end_time - start_time).total_seconds() / 60

        print(f"      📊 正常数据时间范围: {total_minutes:.1f}分钟")

        windows_data = []
        window_count = 0

        # 以3分钟为窗口，滑动切分24小时数据
        current_time = start_time
        while current_time + timedelta(minutes=3) <= end_time:
            window_end = current_time + timedelta(minutes=3)

            # 筛选时间窗口内的数据
            window_data = df[
                (df['date'] >= current_time) &
                (df['date'] < window_end)
            ].copy()

            if not window_data.empty and len(window_data) >= 10:  # 至少10条记录才保存
                window_count += 1
                # 生成窗口文件名
                window_filename = base_filename.replace('.csv', f'_窗口{window_count:03d}.csv')

                windows_data.append({
                    'window_name': f'窗口{window_count:03d}',
                    'description': f'{current_time.strftime("%H:%M:%S")}-{window_end.strftime("%H:%M:%S")}',
                    'filename': window_filename,
                    'data': window_data,
                    'record_count': len(window_data),
                    'time_range': f"{current_time.strftime('%H:%M:%S')} - {window_end.strftime('%H:%M:%S')}"
                })

            # 移动到下一个窗口（3分钟步长）
            current_time += timedelta(minutes=3)

        print(f"      ⏰ 创建了 {len(windows_data)} 个3分钟时间窗口")
        return windows_data

    def process_single_file(self, file_path: Path, data_type: str) -> Dict:
        """
        处理单个数据文件
        """
        print(f"   📄 处理文件: {file_path.name}")

        result = {
            'file_name': file_path.name,
            'success': False,
            'conditions_found': [],
            'windows_created': 0,
            'total_records': 0,
            'filtered_records': 0,
            'error': None
        }

        try:
            # 加载数据
            df = self.load_csv_with_encoding(file_path)
            if df is None or df.empty:
                result['error'] = '文件为空或无法读取'
                return result

            result['total_records'] = len(df)

            # 测试数据特殊处理：添加工况识别但保持混合工况
            if data_type == 'test_data':
                # 为测试数据添加工况识别
                df_with_conditions = self.identify_drilling_condition(df)

                # 测试数据保存到test_data目录，包含工况识别结果
                output_dir = self.output_base_dir / data_type
                output_dir.mkdir(parents=True, exist_ok=True)

                output_file = output_dir / file_path.name
                df_with_conditions.to_csv(output_file, index=False, encoding='utf-8-sig')

                # 统计工况分布
                if 'identified_condition' in df_with_conditions.columns:
                    condition_counts = df_with_conditions['identified_condition'].value_counts()
                    conditions_found = [f"{cond}({count})" for cond, count in condition_counts.items()]
                else:
                    conditions_found = ['工况识别失败']

                result['success'] = True
                result['filtered_records'] = len(df_with_conditions)
                result['conditions_found'] = conditions_found

                print(f"      📁 测试数据（含工况识别）: {len(df_with_conditions)} 条记录")
                print(f"      📊 工况分布: {', '.join(conditions_found)}")
                return result

            # 征兆数据和正常数据按工况分类
            classified_data = self.filter_and_classify_by_condition(df)

            if not classified_data:
                result['error'] = '没有找到目标工况的数据'
                return result

            # 为每种工况创建输出目录和文件
            for condition, condition_data in classified_data.items():
                condition_dir = self.output_base_dir / data_type / condition
                condition_dir.mkdir(parents=True, exist_ok=True)

                result['conditions_found'].append(condition)
                result['filtered_records'] += len(condition_data)

                # 更新统计
                if condition not in self.processing_stats['condition_distribution']:
                    self.processing_stats['condition_distribution'][condition] = 0
                self.processing_stats['condition_distribution'][condition] += len(condition_data)

                # 征兆数据和正常数据都进行时间窗口切分
                if data_type == 'symptom_data':
                    # 征兆数据：15分钟切分为5个3分钟窗口
                    windows_data = self.create_time_windows(condition_data, file_path.name)
                elif data_type == 'normal_data':
                    # 正常数据：24小时切分为多个3分钟窗口
                    windows_data = self.create_time_windows_for_normal_data(condition_data, file_path.name)
                else:
                    windows_data = []

                if windows_data:
                    # 保存每个时间窗口
                    for window_info in windows_data:
                        window_file = condition_dir / window_info['filename']
                        window_info['data'].to_csv(window_file, index=False, encoding='utf-8-sig')
                        result['windows_created'] += 1
                        self.processing_stats['time_windows_created'] += 1

                    print(f"      📁 {condition}: 创建 {len(windows_data)} 个时间窗口")
                else:
                    # 如果无法创建时间窗口，保存原始数据
                    output_file = condition_dir / file_path.name
                    condition_data.to_csv(output_file, index=False, encoding='utf-8-sig')
                    print(f"      📁 {condition}: {len(condition_data)} 条记录（无法切分时间窗口）")

            result['success'] = True

        except Exception as e:
            result['error'] = str(e)
            print(f"      ❌ 处理失败: {e}")

        return result

    def process_all_data(self) -> Dict:
        """
        处理所有数据文件
        """
        print(f"\n🚀 开始处理所有数据文件...")
        print("=" * 60)

        processing_results = {
            'by_data_type': {},
            'summary': {
                'total_files': 0,
                'successful_files': 0,
                'failed_files': 0,
                'conditions_distribution': {},
                'time_windows_created': 0
            }
        }

        for data_type in self.data_types:
            print(f"\n📂 处理 {self.data_type_names[data_type]}...")

            data_dir = self.input_base_dir / data_type
            if not data_dir.exists():
                print(f"   ⚠️ 目录不存在: {data_dir}")
                continue

            csv_files = list(data_dir.glob("*.csv"))
            print(f"   📊 发现 {len(csv_files)} 个CSV文件")

            type_results = {
                'files_processed': 0,
                'files_successful': 0,
                'files_failed': 0,
                'file_details': []
            }

            for i, csv_file in enumerate(csv_files, 1):
                print(f"\n   [{i}/{len(csv_files)}] 处理文件: {csv_file.name}")

                file_result = self.process_single_file(csv_file, data_type)
                type_results['file_details'].append(file_result)
                type_results['files_processed'] += 1

                if file_result['success']:
                    type_results['files_successful'] += 1
                    self.processing_stats['processed_files'] += 1
                else:
                    type_results['files_failed'] += 1
                    self.processing_stats['failed_files'] += 1
                    print(f"      ❌ 失败原因: {file_result['error']}")

                self.processing_stats['total_files'] += 1

            processing_results['by_data_type'][data_type] = type_results

            # 更新总体统计
            processing_results['summary']['total_files'] += type_results['files_processed']
            processing_results['summary']['successful_files'] += type_results['files_successful']
            processing_results['summary']['failed_files'] += type_results['files_failed']

        # 最终统计
        processing_results['summary']['conditions_distribution'] = self.processing_stats['condition_distribution']
        processing_results['summary']['time_windows_created'] = self.processing_stats['time_windows_created']

        return processing_results

    def generate_analysis_report(self, distribution_stats: Dict, processing_results: Dict) -> str:
        """
        生成工况分布分析报告
        """
        report_file = self.output_base_dir / "工况分布分析报告.md"

        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("# 钻井数据工况分布分析报告\n\n")
            f.write(f"**分析时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            # 处理概览
            f.write("## 📊 处理概览\n\n")
            f.write(f"- **输入目录**: `{self.input_base_dir}`\n")
            f.write(f"- **输出目录**: `{self.output_base_dir}`\n")
            f.write(f"- **目标工况**: {', '.join(self.target_conditions)}\n")
            f.write(f"- **处理文件总数**: {processing_results['summary']['total_files']}\n")
            f.write(f"- **成功处理**: {processing_results['summary']['successful_files']}\n")
            f.write(f"- **处理失败**: {processing_results['summary']['failed_files']}\n")
            f.write(f"- **成功率**: {processing_results['summary']['successful_files']/max(processing_results['summary']['total_files'], 1):.2%}\n\n")

            # 原始数据工况分布
            f.write("## 📈 原始数据工况分布\n\n")
            f.write("### 总体分布\n\n")
            f.write("| 工况 | 记录数 | 占比 |\n")
            f.write("|------|--------|------|\n")

            total_records = sum(distribution_stats['overall'].values()) if distribution_stats['overall'] else 1
            for condition, count in sorted(distribution_stats['overall'].items(), key=lambda x: x[1], reverse=True):
                percentage = count / total_records * 100
                f.write(f"| {condition} | {count:,} | {percentage:.2f}% |\n")

            f.write(f"\n**总记录数**: {total_records:,}\n\n")

            # 按数据类型分布
            f.write("### 按数据类型分布\n\n")
            for data_type, stats in distribution_stats['by_data_type'].items():
                type_name = self.data_type_names[data_type]
                f.write(f"#### {type_name}\n\n")
                f.write(f"- **文件数**: {stats['files']}\n")
                f.write(f"- **记录数**: {stats['records']:,}\n\n")

                if stats['conditions']:
                    f.write("| 工况 | 记录数 | 占比 |\n")
                    f.write("|------|--------|------|\n")

                    type_total = sum(stats['conditions'].values())
                    for condition, count in sorted(stats['conditions'].items(), key=lambda x: x[1], reverse=True):
                        percentage = count / type_total * 100 if type_total > 0 else 0
                        f.write(f"| {condition} | {count:,} | {percentage:.2f}% |\n")
                    f.write("\n")

            # 筛选后的数据分布
            f.write("## 🎯 筛选后的数据分布\n\n")
            f.write("### 目标工况统计\n\n")
            f.write("| 工况 | 记录数 | 文件数 |\n")
            f.write("|------|--------|--------|\n")

            condition_files = {}
            for data_type_results in processing_results['by_data_type'].values():
                for file_detail in data_type_results['file_details']:
                    for condition in file_detail['conditions_found']:
                        if condition not in condition_files:
                            condition_files[condition] = 0
                        condition_files[condition] += 1

            for condition in self.target_conditions:
                record_count = processing_results['summary']['conditions_distribution'].get(condition, 0)
                file_count = condition_files.get(condition, 0)
                f.write(f"| {condition} | {record_count:,} | {file_count} |\n")

            # 时间窗口统计（仅征兆数据）
            if processing_results['summary']['time_windows_created'] > 0:
                f.write(f"\n### 时间窗口切分统计\n\n")
                f.write(f"- **创建的时间窗口总数**: {processing_results['summary']['time_windows_created']}\n")
                f.write(f"- **平均每个征兆文件的窗口数**: {processing_results['summary']['time_windows_created'] / max(len([f for f in processing_results['by_data_type'].get('symptom_data', {}).get('file_details', []) if f['success']]), 1):.1f}\n\n")

            # 输出文件结构
            f.write("## 📁 输出文件结构\n\n")
            f.write("```\n")
            f.write("processed_data/\n")
            for data_type in self.data_types:
                type_name = self.data_type_names[data_type]
                f.write(f"├── {data_type}/          # {type_name}\n")
                for condition in self.target_conditions:
                    f.write(f"│   ├── {condition}/\n")
                    if data_type == 'symptom_data':
                        f.write(f"│   │   ├── 井名_时间_征兆数据_窗口1.csv\n")
                        f.write(f"│   │   ├── 井名_时间_征兆数据_窗口2.csv\n")
                        f.write(f"│   │   └── ...\n")
                    else:
                        f.write(f"│   │   └── 井名_时间_{type_name}.csv\n")
            f.write("└── 工况分布分析报告.md\n")
            f.write("```\n\n")

            # 数据使用建议
            f.write("## 💡 数据使用建议\n\n")
            f.write("### 模型训练数据组织\n\n")
            f.write("1. **异常样本**: 使用 `symptom_data/` 中各工况的时间窗口数据\n")
            f.write("2. **正常样本**: 使用 `normal_data/` 中各工况的数据\n")
            f.write("3. **模型验证**: 使用 `test_data/` 中的完整24小时数据\n\n")

            f.write("### 工况平衡建议\n\n")
            condition_counts = processing_results['summary']['conditions_distribution']
            if condition_counts:
                max_count = max(condition_counts.values())
                f.write("各工况样本数量差异较大，建议进行数据平衡处理：\n\n")
                for condition in self.target_conditions:
                    count = condition_counts.get(condition, 0)
                    if count > 0:
                        ratio = count / max_count
                        if ratio < 0.3:
                            f.write(f"- **{condition}**: 样本较少({count:,}条)，建议增加数据或使用数据增强\n")
                        elif ratio > 0.7:
                            f.write(f"- **{condition}**: 样本充足({count:,}条)\n")
                        else:
                            f.write(f"- **{condition}**: 样本适中({count:,}条)\n")

            f.write("\n### 下一步处理建议\n\n")
            f.write("1. 检查各工况的数据质量和时间连续性\n")
            f.write("2. 根据模型需求进行数据标准化和特征工程\n")
            f.write("3. 考虑工况间的样本平衡问题\n")
            f.write("4. 验证时间窗口切分的合理性\n")

        print(f"\n📋 分析报告已生成: {report_file}")
        return str(report_file)


def main():
    """
    主函数 - 钻井数据后处理
    """
    # 配置参数
    INPUT_DIR = r"D:\采数据\PROCESS\stuck_prediction_data"  # 原始导出数据目录
    OUTPUT_DIR = r"D:\采数据\PROCESS\processed_data"  # 处理后数据目录

    print("🚀 钻井数据后处理器")
    print("=" * 50)
    print(f"📂 输入目录: {INPUT_DIR}")
    print(f"📁 输出目录: {OUTPUT_DIR}")
    print(f"🎯 目标工况: 正常钻进、起钻、下钻、正划眼、倒划眼")
    print("=" * 50)

    # 检查输入目录
    input_path = Path(INPUT_DIR)
    if not input_path.exists():
        print(f"❌ 输入目录不存在: {INPUT_DIR}")
        print("   请先运行 drilling_stuck_data_exporter.py 导出原始数据")
        return

    # 检查必要的子目录
    required_dirs = ['symptom_data', 'test_data', 'normal_data']
    missing_dirs = []
    for dir_name in required_dirs:
        if not (input_path / dir_name).exists():
            missing_dirs.append(dir_name)

    if missing_dirs:
        print(f"⚠️ 缺少必要的数据目录: {', '.join(missing_dirs)}")
        print("   请检查原始数据导出是否完整")

    try:
        # 初始化后处理器
        processor = DrillingDataPostProcessor(INPUT_DIR, OUTPUT_DIR)

        # 第一步：分析原始数据的工况分布
        print(f"\n🔍 第一步：分析原始数据工况分布")
        distribution_stats = processor.analyze_condition_distribution()

        if distribution_stats['file_count'] == 0:
            print("❌ 未找到任何数据文件，请检查输入目录")
            return

        print(f"\n📊 原始数据统计:")
        print(f"   - 总文件数: {distribution_stats['file_count']}")
        print(f"   - 总记录数: {distribution_stats['total_records']:,}")
        print(f"   - 发现工况: {len(distribution_stats['overall'])} 种")

        # 显示工况分布
        print(f"\n🏷️ 工况分布 (Top 10):")
        sorted_conditions = sorted(distribution_stats['overall'].items(),
                                 key=lambda x: x[1], reverse=True)[:10]
        for condition, count in sorted_conditions:
            percentage = count / distribution_stats['total_records'] * 100
            print(f"   - {condition}: {count:,} ({percentage:.1f}%)")

        # 第二步：处理所有数据文件
        print(f"\n🔄 第二步：按工况筛选和分类数据")
        processing_results = processor.process_all_data()

        # 第三步：生成分析报告
        print(f"\n📋 第三步：生成分析报告")
        report_file = processor.generate_analysis_report(distribution_stats, processing_results)

        # 输出处理结果
        print(f"\n🎉 数据后处理完成!")
        print("=" * 50)

        print(f"📊 处理统计:")
        print(f"   - 处理文件: {processing_results['summary']['total_files']} 个")
        print(f"   - 成功处理: {processing_results['summary']['successful_files']} 个")
        print(f"   - 处理失败: {processing_results['summary']['failed_files']} 个")
        print(f"   - 成功率: {processing_results['summary']['successful_files']/max(processing_results['summary']['total_files'], 1):.2%}")

        if processing_results['summary']['time_windows_created'] > 0:
            print(f"   - 创建时间窗口: {processing_results['summary']['time_windows_created']} 个")

        print(f"\n🎯 目标工况数据统计:")
        for condition in processor.target_conditions:
            count = processing_results['summary']['conditions_distribution'].get(condition, 0)
            if count > 0:
                print(f"   - {condition}: {count:,} 条记录")
            else:
                print(f"   - {condition}: 无数据")

        print(f"\n📁 输出目录: {OUTPUT_DIR}")
        print(f"📋 详细报告: {report_file}")

        # 输出文件结构预览
        print(f"\n📂 输出文件结构:")
        output_path = Path(OUTPUT_DIR)
        if output_path.exists():
            for data_type in processor.data_types:
                data_dir = output_path / data_type
                if data_dir.exists():
                    print(f"   📁 {data_type}/")
                    for condition_dir in data_dir.iterdir():
                        if condition_dir.is_dir():
                            file_count = len(list(condition_dir.glob("*.csv")))
                            print(f"      📁 {condition_dir.name}/ ({file_count} 个文件)")

        print(f"\n💡 下一步建议:")
        print(f"   1. 查看详细分析报告了解数据分布情况")
        print(f"   2. 检查各工况的数据质量和数量平衡")
        print(f"   3. 根据模型需求进行进一步的数据预处理")
        print(f"   4. 考虑数据增强或重采样来平衡各工况样本")

    except Exception as e:
        print(f"❌ 处理过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
