#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一钻井卡钻数据处理器
整合数据提取、工况识别、数据筛选和输出功能的一体化处理脚本

功能：
1. 从原始数据库提取钻井卡钻数据（三种类型：征兆、测试、正常）
2. 基于钻井参数自动识别工况（替代RIGSTA字段依赖）
3. 按5种核心工况筛选和分类数据
4. 对征兆数据进行3分钟时间窗口切分
5. 输出处理好的最终数据和详细报告

优势：
- 一次执行完成完整的数据处理流程
- 消除中间文件依赖，提高处理效率
- 保持原有功能完整性和数据格式
"""

import sqlite3
import csv
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import json
import os
import shutil
import warnings
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import silhouette_score
from scipy import stats
warnings.filterwarnings('ignore')

class SmartConditionRecognizer:
    """
    智能工况识别器 - 基于多参数融合和无监督学习的工况识别
    """

    def __init__(self, sampling_frequency: float = 0.5):
        """
        初始化智能工况识别器

        参数:
            sampling_frequency: 数据采样频率 (Hz)，默认0.5Hz (2秒一个点)
        """
        self.sampling_frequency = sampling_frequency

        # 动态时间窗口配置
        self.time_windows = {
            'short': int(30 * sampling_frequency),    # 30秒窗口
            'medium': int(120 * sampling_frequency),  # 2分钟窗口
            'long': int(300 * sampling_frequency)     # 5分钟窗口
        }

        # 扩展参数权重配置（基于钻井工艺重要性）
        self.parameter_weights = {
            'DEP': 0.25,      # 井深 - 核心参数
            'BITDEP': 0.20,   # 钻头位置 - 核心参数
            'RPM': 0.15,      # 转速 - 重要参数
            'WOB': 0.15,      # 钻压 - 重要参数
            'TOR': 0.10,      # 扭矩 - 辅助参数
            'SPP': 0.10,      # 立管压力 - 辅助参数
            'HKLD': 0.05      # 大钩载荷 - 辅助参数
        }

        # 动态阈值配置（优化正常钻进识别）
        self.adaptive_thresholds = {
            'depth_change_base': 0.01,        # 基础井深变化阈值(米) - 降低到1厘米
            'depth_change_factor': 0.00005,   # 井深相关系数 - 大幅降低井深影响
            'bitdep_change_base': 0.15,       # 基础钻头位置变化阈值(米) - 略微降低
            'rpm_low_threshold': 8.0,         # 低转速阈值(rpm) - 略微提高
            'rpm_high_threshold': 40.0,       # 高转速阈值(rpm) - 降低以识别更多划眼
            'wob_change_threshold': 5.0,      # 钻压变化阈值(kN)
            'tor_change_threshold': 2.0,      # 扭矩变化阈值(kN·m)
            'spp_change_threshold': 0.5       # 立管压力变化阈值(MPa)
        }

        # 聚类配置
        self.clustering_config = {
            'n_clusters': 6,                  # 聚类数量（5个目标工况+1个其他）
            'max_iter': 300,                  # 最大迭代次数
            'n_init': 10,                     # 初始化次数
            'random_state': 42                # 随机种子
        }

        # 置信度评估配置
        self.confidence_config = {
            'min_samples_per_condition': 5,   # 每个工况最少样本数
            'stability_window': 10,           # 稳定性评估窗口
            'noise_threshold': 0.3            # 噪声阈值
        }

        # 工况映射
        self.condition_mapping = {
            0: '正常钻进',
            1: '起钻',
            2: '下钻',
            3: '正划眼',
            4: '倒划眼',
            5: '其他'
        }

        # 初始化模型组件
        self.scaler = StandardScaler()
        self.kmeans = None
        self.feature_importance = None

    def calculate_dynamic_thresholds(self, df: pd.DataFrame) -> Dict[str, float]:
        """
        基于数据特征计算动态阈值
        """
        thresholds = self.adaptive_thresholds.copy()

        if 'DEP' in df.columns and not df['DEP'].empty:
            # 根据井深调整阈值
            avg_depth = df['DEP'].mean()
            thresholds['depth_change_threshold'] = (
                thresholds['depth_change_base'] +
                avg_depth * thresholds['depth_change_factor']
            )
        else:
            thresholds['depth_change_threshold'] = thresholds['depth_change_base']

        return thresholds

    def extract_multi_scale_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        提取多尺度特征
        """
        if df.empty:
            return df

        features_df = df.copy()
        available_params = [col for col in self.parameter_weights.keys() if col in df.columns]

        # 为每个时间尺度提取特征
        for scale_name, window_size in self.time_windows.items():
            for param in available_params:
                if param in df.columns:
                    # 变化值特征
                    features_df[f'{param}_change_{scale_name}'] = df[param].rolling(
                        window=window_size, min_periods=1
                    ).apply(lambda x: x.iloc[-1] - x.iloc[0] if len(x) > 1 else 0)

                    # 变化率特征
                    features_df[f'{param}_rate_{scale_name}'] = df[param].rolling(
                        window=window_size, min_periods=1
                    ).apply(lambda x: (x.iloc[-1] - x.iloc[0]) / len(x) if len(x) > 1 else 0)

                    # 标准差特征（稳定性指标）
                    features_df[f'{param}_std_{scale_name}'] = df[param].rolling(
                        window=window_size, min_periods=1
                    ).std().fillna(0)

                    # 变化加速度特征（修复数组处理问题）
                    if window_size >= 3:
                        def safe_acceleration(x):
                            if len(x) >= 3:
                                try:
                                    grad1 = np.gradient(x)
                                    grad2 = np.gradient(grad1)
                                    return grad2[-1] if len(grad2) > 0 else 0  # 返回最后一个值
                                except:
                                    return 0
                            else:
                                return 0

                        features_df[f'{param}_accel_{scale_name}'] = df[param].rolling(
                            window=window_size, min_periods=3
                        ).apply(safe_acceleration)

        return features_df

    def hybrid_condition_recognition(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        混合工况识别：无监督聚类 + 规则验证
        """
        if df.empty:
            return df

        # 提取多尺度特征
        features_df = self.extract_multi_scale_features(df)

        # 选择用于聚类的特征
        feature_columns = []
        available_params = [col for col in self.parameter_weights.keys() if col in df.columns]

        for param in available_params:
            for scale in self.time_windows.keys():
                feature_columns.extend([
                    f'{param}_change_{scale}',
                    f'{param}_rate_{scale}',
                    f'{param}_std_{scale}'
                ])

        # 过滤存在的特征列
        feature_columns = [col for col in feature_columns if col in features_df.columns]

        if not feature_columns:
            print("⚠️ 无法提取有效特征，使用传统规则识别")
            return self._fallback_rule_recognition(df)

        # 准备聚类数据
        X = features_df[feature_columns].fillna(0)

        # 检查数据有效性
        if X.empty or X.shape[0] < self.clustering_config['n_clusters']:
            print(f"   ⚠️ 数据量不足进行聚类 ({X.shape[0]} < {self.clustering_config['n_clusters']})")
            return self._fallback_rule_recognition(df)

        # 检查是否有无穷大或NaN值
        X = X.replace([np.inf, -np.inf], 0)
        X = X.fillna(0)

        # 标准化特征
        try:
            X_scaled = self.scaler.fit_transform(X)
        except Exception as e:
            print(f"   ⚠️ 特征标准化失败: {e}")
            return self._fallback_rule_recognition(df)

        # 执行K-means聚类
        try:
            self.kmeans = KMeans(**self.clustering_config)
            cluster_labels = self.kmeans.fit_predict(X_scaled)
        except Exception as e:
            print(f"   ⚠️ 聚类执行失败: {e}")
            return self._fallback_rule_recognition(df)

        # 计算聚类质量
        if len(np.unique(cluster_labels)) > 1:
            silhouette_avg = silhouette_score(X_scaled, cluster_labels)
            print(f"   🎯 聚类质量评分: {silhouette_avg:.3f}")
        else:
            silhouette_avg = 0.0
            print("   ⚠️ 聚类结果单一，质量评分无法计算")

        # 将聚类结果映射到工况
        condition_labels = self._map_clusters_to_conditions(
            features_df, cluster_labels, available_params
        )

        # 规则验证和修正
        validated_labels = self._validate_with_rules(
            features_df, condition_labels, available_params
        )

        # 计算置信度
        confidence_scores = self._calculate_confidence(
            features_df, validated_labels, X_scaled
        )

        # 添加结果到数据框
        result_df = df.copy()
        result_df['identified_condition'] = validated_labels
        result_df['confidence_score'] = confidence_scores
        result_df['cluster_label'] = cluster_labels

        return result_df

    def _fallback_rule_recognition(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        传统规则识别（备用方案）
        """
        result_df = df.copy()

        # 使用简化的规则识别
        conditions = []
        for _, row in df.iterrows():
            if 'DEP' in row and 'RPM' in row:
                # 简单规则判断
                if pd.notna(row.get('DEP', 0)) and row.get('DEP', 0) > 0:
                    conditions.append('正常钻进')
                elif pd.notna(row.get('RPM', 0)) and row.get('RPM', 0) > 10:
                    conditions.append('正划眼')
                else:
                    conditions.append('其他')
            else:
                conditions.append('其他')

        result_df['identified_condition'] = conditions
        result_df['confidence_score'] = 0.5  # 低置信度
        result_df['cluster_label'] = -1      # 无聚类标签

        return result_df

    def _map_clusters_to_conditions(self, features_df: pd.DataFrame,
                                   cluster_labels: np.ndarray,
                                   available_params: List[str]) -> List[str]:
        """
        将聚类结果映射到具体工况
        """
        condition_mapping = []

        # 分析每个聚类的特征
        print(f"   📊 聚类分析详情:")
        for cluster_id in np.unique(cluster_labels):
            cluster_mask = cluster_labels == cluster_id
            cluster_data = features_df[cluster_mask]
            cluster_size = len(cluster_data)

            # 基于聚类特征判断工况类型
            condition = self._analyze_cluster_characteristics(
                cluster_data, available_params
            )
            condition_mapping.append((cluster_id, condition))

            # 显示聚类统计信息
            dep_change_avg = cluster_data.get('DEP_change_short', pd.Series([0])).mean()
            bitdep_change_avg = cluster_data.get('BITDEP_change_short', pd.Series([0])).mean()
            rpm_avg = cluster_data.get('RPM', pd.Series([0])).mean()

            print(f"      聚类{cluster_id}: {cluster_size}样本 → {condition}")
            print(f"        井深变化: {dep_change_avg:.3f}m, 钻头变化: {bitdep_change_avg:.3f}m, 转速: {rpm_avg:.1f}rpm")

        # 创建映射字典
        cluster_to_condition = dict(condition_mapping)

        # 应用映射
        mapped_conditions = [
            cluster_to_condition.get(label, '其他')
            for label in cluster_labels
        ]

        return mapped_conditions

    def _analyze_cluster_characteristics(self, cluster_data: pd.DataFrame,
                                       available_params: List[str]) -> str:
        """
        分析聚类特征以确定工况类型
        """
        if cluster_data.empty:
            return '其他'

        # 计算关键参数的平均变化
        dep_change = 0
        bitdep_change = 0
        rpm_avg = 0

        # 使用短期窗口的变化值
        if 'DEP_change_short' in cluster_data.columns:
            dep_change = cluster_data['DEP_change_short'].mean()
        elif 'DEP' in cluster_data.columns:
            dep_change = cluster_data['DEP'].diff().mean()

        if 'BITDEP_change_short' in cluster_data.columns:
            bitdep_change = cluster_data['BITDEP_change_short'].mean()
        elif 'BITDEP' in cluster_data.columns:
            bitdep_change = cluster_data['BITDEP'].diff().mean()

        if 'RPM' in cluster_data.columns:
            rpm_avg = cluster_data['RPM'].mean()

        # 应用改进的判断规则
        thresholds = self.calculate_dynamic_thresholds(cluster_data)

        # 优化的工况判断逻辑
        # 1. 首先判断是否为钻进（最重要的工况）
        if dep_change > thresholds['depth_change_threshold']:
            return '正常钻进'

        # 2. 判断是否为明显的井深下降（异常情况，归为其他）
        elif dep_change < -thresholds['depth_change_threshold']:
            return '其他'

        # 3. 井深基本不变的情况（起下钻、划眼等）
        else:
            # 3.1 低转速 + 钻头位置变化 = 起下钻
            if rpm_avg <= thresholds['rpm_low_threshold']:
                if bitdep_change > thresholds['bitdep_change_base']:
                    return '下钻'
                elif bitdep_change < -thresholds['bitdep_change_base']:
                    return '起钻'
                else:
                    # 低转速但钻头位置不变，可能是停钻或其他
                    return '其他'

            # 3.2 高转速 + 钻头位置变化 = 划眼
            elif rpm_avg >= thresholds['rpm_high_threshold']:
                if bitdep_change > thresholds['bitdep_change_base']:
                    return '正划眼'
                elif bitdep_change < -thresholds['bitdep_change_base']:
                    return '倒划眼'
                else:
                    # 高转速但钻头位置不变，可能是原地转动
                    return '其他'

            # 3.3 中等转速的情况
            else:
                # 中等转速下，首先检查钻头位置变化（可能是低转速划眼）
                if bitdep_change > thresholds['bitdep_change_base']:
                    return '正划眼'  # 中等转速下钻进
                elif bitdep_change < -thresholds['bitdep_change_base']:
                    return '倒划眼'  # 中等转速倒划眼
                # 钻头位置变化不大时，检查井深变化
                elif dep_change > thresholds['depth_change_threshold'] * 0.3:
                    return '正常钻进'  # 慢速钻进
                else:
                    return '其他'

    def _validate_with_rules(self, features_df: pd.DataFrame,
                           condition_labels: List[str],
                           available_params: List[str]) -> List[str]:
        """
        使用物理规则验证和修正聚类结果
        """
        validated_labels = condition_labels.copy()

        # 应用物理约束规则
        for i, (_, row) in enumerate(features_df.iterrows()):
            current_condition = condition_labels[i]

            # 规则1：钻进时井深必须增加
            if current_condition == '正常钻进':
                dep_change = row.get('DEP_change_short', 0)
                if dep_change <= 0:
                    validated_labels[i] = '其他'

            # 规则2：起钻时钻头位置应该上升
            elif current_condition == '起钻':
                bitdep_change = row.get('BITDEP_change_short', 0)
                if bitdep_change >= 0:
                    validated_labels[i] = '其他'

            # 规则3：下钻时钻头位置应该下降
            elif current_condition == '下钻':
                bitdep_change = row.get('BITDEP_change_short', 0)
                if bitdep_change <= 0:
                    validated_labels[i] = '其他'

        return validated_labels

    def _calculate_confidence(self, features_df: pd.DataFrame,
                            condition_labels: List[str],
                            X_scaled: np.ndarray) -> List[float]:
        """
        计算工况识别的置信度
        """
        confidence_scores = []

        for i, condition in enumerate(condition_labels):
            confidence = 0.5  # 基础置信度

            # 基于聚类距离的置信度
            if self.kmeans is not None and i < len(X_scaled):
                try:
                    sample = X_scaled[i].reshape(1, -1)
                    distances = self.kmeans.transform(sample)
                    min_distance = np.min(distances)

                    # 距离越小，置信度越高（避免除零错误）
                    if not np.isnan(min_distance) and not np.isinf(min_distance):
                        distance_confidence = max(0.0, 1.0 - min_distance / 10.0)
                        confidence += distance_confidence * 0.3
                except Exception:
                    # 如果计算失败，跳过距离置信度
                    pass

            # 基于工况稳定性的置信度
            window_start = max(0, i - self.confidence_config['stability_window'])
            window_end = min(len(condition_labels), i + self.confidence_config['stability_window'])
            window_conditions = condition_labels[window_start:window_end]

            # 计算窗口内相同工况的比例
            same_condition_ratio = window_conditions.count(condition) / len(window_conditions)
            stability_confidence = same_condition_ratio * 0.2
            confidence += stability_confidence

            # 限制置信度范围
            confidence = max(0.0, min(1.0, confidence))
            confidence_scores.append(confidence)

        return confidence_scores

class UnifiedDrillingDataProcessor:
    """
    统一钻井卡钻数据处理器
    整合数据提取和后处理功能
    """
    
    def __init__(self, base_path: str, output_base_dir: str, sampling_frequency: float = 0.5):
        self.base_path = Path(base_path)
        self.output_base_dir = Path(output_base_dir)

        # 初始化智能工况识别器
        self.smart_recognizer = SmartConditionRecognizer(sampling_frequency)
        
        # 数据导出配置（来自原exporter）
        self.export_configs = {
            'symptom': {
                'name': '征兆数据',
                'time_before_minutes': 15,  # 卡钻前15分钟
                'folder': 'symptom_data',
                'description': '用于异常样本训练'
            },
            'test': {
                'name': '测试数据', 
                'time_before_hours': 24,    # 卡钻前24小时
                'folder': 'test_data',
                'description': '用于模型性能验证'
            },
            'normal': {
                'name': '正常数据',
                'time_before_days_start': 7,   # 卡钻前7天开始
                'time_before_days_end': 30,    # 卡钻前30天结束
                'sample_hours': 6,             # 采样时长改为6小时
                'folder': 'normal_data',
                'description': '用于正常样本训练'
            }
        }
        
        # 必需的数据字段
        self.required_fields = [
            'DEP', 'BITDEP', 'HOKHEI', 'DRITIME', 'WOB', 'HKLD',
            'RPM', 'TOR', 'SPP', 'CSIP'
        ]
        
        # 目标工况列表（来自原postprocessor）
        self.target_conditions = [
            '正常钻进',  # 对应原来的'钻进'
            '起钻',
            '下钻', 
            '正划眼',
            '倒划眼'
        ]
        
        # 工况映射（标准化不同的表达方式）
        self.condition_mapping = {
            '正常钻进': ['正常钻进', '钻进', '钻井', 'DRILLING', '钻', '进'],
            '起钻': ['起钻', '上提', '起下钻', 'TRIP_OUT', '起', '上'],
            '下钻': ['下钻', '下放', '下钻具', 'TRIP_IN', '下', '入'],
            '正划眼': ['正划眼', '划眼', '扩眼', 'REAMING', '划', '扩'],
            '倒划眼': ['倒划眼', '反划眼', 'BACK_REAMING', '倒', '反']
        }
        
        # 工况识别参数配置（优化正常钻进识别率）
        self.condition_thresholds = {
            'time_window_points': 10,           # 固定数据点窗口（约10分钟）
            'depth_change_threshold': 0.1,      # 井深变化阈值(米) - 降低以提高钻进识别率
            'bitdep_change_threshold': 0.3,     # 钻头位置变化阈值(米)
            'rpm_threshold': 10.0,              # 转速阈值(rpm)
        }
        
        # 时间窗口配置（适用于所有数据类型）
        self.window_config = {
            'window_duration_minutes': 3,       # 时间窗口长度（分钟）
            'min_records_per_window': 5,        # 每个窗口最少记录数
            'test_data_min_duration_minutes': 3, # 测试数据连续工况最少持续时间才切分
        }
        
        # 创建输出目录
        self.output_base_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"🔧 初始化统一钻井数据处理器")
        print(f"   - 数据库根路径: {self.base_path}")
        print(f"   - 输出根目录: {self.output_base_dir}")
        print(f"   - 目标工况: {', '.join(self.target_conditions)}")
        print(f"   - 工况识别: 基于钻井参数自动识别")
    
    def load_stuck_events_from_csv(self, csv_file: str) -> List[Dict]:
        """
        从CSV文件加载卡钻事件信息
        
        CSV格式要求：
        - 井名: 井的名称
        - 卡钻时间: 卡钻发生的时间 (YYYY-MM-DD HH:MM:SS)
        - 事件描述: 可选，事件的描述信息
        """
        events = []
        
        if not os.path.exists(csv_file):
            print(f"❌ 卡钻事件CSV文件不存在: {csv_file}")
            return events
        
        try:
            # 尝试多种编码
            encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
            df = None
            
            for encoding in encodings:
                try:
                    df = pd.read_csv(csv_file, encoding=encoding)
                    print(f"✅ 使用 {encoding} 编码成功读取CSV")
                    break
                except UnicodeDecodeError:
                    continue
            
            if df is None:
                print(f"❌ 无法读取CSV文件，尝试了所有编码")
                return events
            
            # 检查必要列
            required_columns = ['井名', '卡钻时间']
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                print(f"❌ CSV文件缺少必要列: {missing_columns}")
                print(f"   实际列名: {list(df.columns)}")
                return events
            
            for _, row in df.iterrows():
                try:
                    # 解析时间
                    stuck_time_str = str(row['卡钻时间']).strip()
                    stuck_time = pd.to_datetime(stuck_time_str)
                    
                    event = {
                        'well_name': str(row['井名']).strip(),
                        'stuck_time': stuck_time,
                        'description': str(row.get('事件描述', '')).strip() if '事件描述' in row else ''
                    }
                    
                    events.append(event)
                    
                except Exception as e:
                    print(f"⚠️ 跳过无效事件记录: {e}")
                    continue
            
            print(f"✅ 成功加载 {len(events)} 个卡钻事件")
            return events
            
        except Exception as e:
            print(f"❌ 读取卡钻事件CSV文件失败: {e}")
            return events
    
    def get_db_files_for_time_range(self, well_path: Path, start_time: datetime, end_time: datetime) -> List[Path]:
        """
        获取指定时间范围内的数据库文件
        """
        db_files = []

        # 生成需要查询的月份（参照原始exporter脚本逻辑）
        current = datetime(start_time.year, start_time.month, 1)
        end_month = datetime(end_time.year, end_time.month, 1)

        while current <= end_month:
            # 数据库文件名格式: 0YYMM.db
            db_filename = f"0{current.strftime('%y%m')}.db"
            db_path = well_path / db_filename

            if db_path.exists():
                db_files.append(db_path)
            else:
                print(f"⚠️ 数据库文件不存在: {db_path}")

            # 移动到下个月
            if current.month == 12:
                current = datetime(current.year + 1, 1, 1)
            else:
                current = datetime(current.year, current.month + 1, 1)

        return db_files
    
    def extract_data_from_db(self, db_files: List[Path], start_time: datetime, end_time: datetime) -> pd.DataFrame:
        """
        从数据库文件中提取指定时间范围的数据
        （参照原始exporter脚本逻辑）
        """
        all_data = []

        for db_file in db_files:
            try:
                conn = sqlite3.connect(str(db_file))

                # 构建SQL查询 - 使用原始脚本的逻辑
                fields = self.required_fields
                field_str = ', '.join(fields)

                sql = f"""
                SELECT {field_str}, (WELLDATE || ' ' || WELLTIME) AS date
                FROM data
                WHERE datetime(WELLDATE || ' ' || WELLTIME)
                    BETWEEN '{start_time.strftime('%Y-%m-%d %H:%M:%S')}'
                    AND '{end_time.strftime('%Y-%m-%d %H:%M:%S')}'
                ORDER BY datetime(WELLDATE || ' ' || WELLTIME)
                """

                df = pd.read_sql_query(sql, conn)

                if not df.empty:
                    all_data.append(df)
                    print(f"  📊 从 {db_file.name} 提取 {len(df)} 条记录")

                conn.close()

            except Exception as e:
                print(f"⚠️ 读取数据库文件失败 {db_file.name}: {e}")
                continue

        if all_data:
            combined_df = pd.concat(all_data, ignore_index=True)
            combined_df = combined_df.sort_values('date').reset_index(drop=True)
            return combined_df
        else:
            return pd.DataFrame()

    def identify_drilling_condition(self, df: pd.DataFrame, use_smart_recognition: bool = True) -> pd.DataFrame:
        """
        基于钻井参数自动识别工况 - 支持智能识别和传统规则识别

        参数:
            df: 输入数据
            use_smart_recognition: 是否使用智能识别算法

        智能识别特点:
        1. 多尺度时间窗口分析（30秒、2分钟、5分钟）
        2. 多参数融合（DEP、BITDEP、RPM、WOB、TOR、SPP等）
        3. 无监督聚类 + 规则验证
        4. 动态阈值调整
        5. 置信度评估
        """
        if df.empty:
            return df

        # 确保必要字段存在
        basic_fields = ['DEP', 'BITDEP', 'RPM', 'date']
        missing_fields = [field for field in basic_fields if field not in df.columns]
        if missing_fields:
            print(f"⚠️ 缺少必要字段进行工况识别: {missing_fields}")
            df_result = df.copy()
            df_result['identified_condition'] = '其他'
            df_result['confidence_score'] = 0.0
            return df_result

        # 转换时间字段
        df = df.copy()
        df['datetime'] = pd.to_datetime(df['date'])
        df = df.sort_values('datetime').reset_index(drop=True)

        print(f"   🤖 工况识别模式: {'智能识别' if use_smart_recognition else '传统规则'}")

        if use_smart_recognition:
            try:
                # 使用智能识别算法
                result_df = self.smart_recognizer.hybrid_condition_recognition(df)

                # 统计识别结果
                condition_counts = result_df['identified_condition'].value_counts()
                avg_confidence = result_df['confidence_score'].mean()

                print(f"   📊 识别结果统计: {dict(condition_counts)}")
                print(f"   🎯 平均置信度: {avg_confidence:.3f}")

                return result_df

            except Exception as e:
                print(f"   ⚠️ 智能识别失败，回退到传统规则: {e}")
                use_smart_recognition = False

        if not use_smart_recognition:
            # 使用传统规则识别（优化版本）
            return self._traditional_rule_recognition(df)

    def _traditional_rule_recognition(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        传统规则识别（优化版本）
        """
        # 计算动态阈值
        dynamic_thresholds = self.smart_recognizer.calculate_dynamic_thresholds(df)

        # 使用自适应时间窗口
        window_size = self.smart_recognizer.time_windows['short']  # 30秒窗口

        df['dep_change'] = df['DEP'].rolling(window=window_size, min_periods=1).apply(
            lambda x: x.iloc[-1] - x.iloc[0] if len(x) > 1 else 0
        )

        df['bitdep_change'] = df['BITDEP'].rolling(window=window_size, min_periods=1).apply(
            lambda x: x.iloc[-1] - x.iloc[0] if len(x) > 1 else 0
        )

        # 应用优化的工况识别规则
        conditions = []
        confidence_scores = []

        for _, row in df.iterrows():
            dep_change = row['dep_change']
            bitdep_change = row['bitdep_change']
            rpm = row['RPM']

            # 处理NaN值
            if pd.isna(dep_change) or pd.isna(bitdep_change) or pd.isna(rpm):
                conditions.append('其他')
                confidence_scores.append(0.1)
                continue

            # 应用优化的动态阈值判断规则
            confidence = 0.6  # 基础置信度

            if dep_change > dynamic_thresholds['depth_change_threshold']:
                # 井深增加 -> 钻进
                conditions.append('正常钻进')
                confidence += 0.25
            elif dep_change < -dynamic_thresholds['depth_change_threshold']:
                # 井深下降 -> 异常情况
                conditions.append('其他')
                confidence -= 0.2
            else:
                # 井深基本不变
                if rpm <= dynamic_thresholds['rpm_low_threshold']:
                    # 转速低
                    if bitdep_change > dynamic_thresholds['bitdep_change_base']:
                        conditions.append('下钻')
                        confidence += 0.15
                    elif bitdep_change < -dynamic_thresholds['bitdep_change_base']:
                        conditions.append('起钻')
                        confidence += 0.15
                    else:
                        conditions.append('其他')
                        confidence -= 0.2
                elif rpm >= dynamic_thresholds['rpm_high_threshold']:
                    # 转速高
                    if bitdep_change > dynamic_thresholds['bitdep_change_base']:
                        conditions.append('正划眼')
                        confidence += 0.15
                    elif bitdep_change < -dynamic_thresholds['bitdep_change_base']:
                        conditions.append('倒划眼')
                        confidence += 0.15
                    else:
                        conditions.append('其他')
                        confidence -= 0.2
                else:
                    # 中等转速情况：检查钻头位置变化
                    if bitdep_change > dynamic_thresholds['bitdep_change_base']:
                        conditions.append('正划眼')
                        confidence += 0.1
                    elif bitdep_change < -dynamic_thresholds['bitdep_change_base']:
                        conditions.append('倒划眼')
                        confidence += 0.1
                    elif dep_change > dynamic_thresholds['depth_change_threshold'] * 0.3:
                        conditions.append('正常钻进')
                        confidence += 0.05
                    else:
                        conditions.append('其他')
                        confidence -= 0.1

            # 限制置信度范围
            confidence = max(0.0, min(1.0, confidence))
            confidence_scores.append(confidence)

        df['identified_condition'] = conditions
        df['confidence_score'] = confidence_scores
        df['cluster_label'] = -1  # 传统规则无聚类标签

        # 统计识别结果
        condition_counts = pd.Series(conditions).value_counts()
        avg_confidence = np.mean(confidence_scores)

        print(f"   📊 识别结果统计: {dict(condition_counts)}")
        print(f"   🎯 平均置信度: {avg_confidence:.3f}")

        return df

    def filter_by_target_conditions(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        按目标工况筛选数据
        """
        if df.empty or 'identified_condition' not in df.columns:
            return df

        # 只保留目标工况的数据
        filtered_df = df[df['identified_condition'].isin(self.target_conditions)].copy()

        return filtered_df

    def validate_data_quality(self, df: pd.DataFrame) -> Dict:
        """
        验证数据质量
        """
        if df.empty:
            return {
                'quality_score': 0.0,
                'total_records': 0,
                'valid_records': 0,
                'missing_data_ratio': 1.0,
                'issues': ['数据为空']
            }

        total_records = len(df)
        issues = []

        # 检查必需字段的缺失情况
        missing_ratios = {}
        for field in self.required_fields:
            if field in df.columns:
                missing_count = df[field].isna().sum()
                missing_ratio = missing_count / total_records
                missing_ratios[field] = missing_ratio

                if missing_ratio > 0.1:  # 超过10%缺失
                    issues.append(f'{field}字段缺失率过高: {missing_ratio:.2%}')

        # 计算整体缺失率
        overall_missing_ratio = np.mean(list(missing_ratios.values())) if missing_ratios else 1.0

        # 检查数据范围合理性
        if 'DEP' in df.columns:
            dep_values = df['DEP'].dropna()
            if not dep_values.empty:
                if dep_values.min() < 0:
                    issues.append('井深存在负值')
                if dep_values.max() > 10000:  # 假设最大井深10000米
                    issues.append('井深存在异常大值')

        # 计算质量分数
        quality_score = max(0.0, 1.0 - overall_missing_ratio)
        if issues:
            quality_score *= 0.8  # 有问题时降低分数

        valid_records = total_records - int(total_records * overall_missing_ratio)

        return {
            'quality_score': quality_score,
            'total_records': total_records,
            'valid_records': valid_records,
            'missing_data_ratio': overall_missing_ratio,
            'missing_ratios': missing_ratios,
            'issues': issues
        }

    def split_data_by_time_windows(self, df: pd.DataFrame, data_type: str) -> List[Dict]:
        """
        按时间窗口切分数据（支持所有数据类型）

        参数:
            df: 要切分的数据
            data_type: 数据类型 ('symptom', 'test', 'normal')
        """
        if df.empty:
            return []

        # 确保时间字段存在
        if 'datetime' not in df.columns:
            df['datetime'] = pd.to_datetime(df['date'])

        df = df.sort_values('datetime').reset_index(drop=True)

        if data_type == 'test':
            # 测试数据特殊处理：按连续工况切分
            return self._split_test_data_by_continuous_conditions(df)
        else:
            # 征兆数据和正常数据：固定3分钟窗口切分
            return self._split_data_by_fixed_windows(df)

    def _split_data_by_fixed_windows(self, df: pd.DataFrame) -> List[Dict]:
        """
        按固定3分钟窗口切分数据（征兆数据和正常数据）
        """
        window_duration = timedelta(minutes=self.window_config['window_duration_minutes'])
        min_records = self.window_config['min_records_per_window']

        start_time = df['datetime'].min()
        end_time = df['datetime'].max()

        windows = []
        current_time = start_time
        window_count = 0

        while current_time < end_time:
            window_end = current_time + window_duration

            # 提取当前窗口的数据
            window_data = df[
                (df['datetime'] >= current_time) &
                (df['datetime'] < window_end)
            ].copy()

            # 只保留记录数足够的窗口
            if len(window_data) >= min_records:
                window_count += 1
                time_range = f"{current_time.strftime('%H:%M:%S')}-{window_end.strftime('%H:%M:%S')}"

                windows.append({
                    'window_name': f'窗口{window_count:03d}',
                    'time_range': time_range,
                    'data': window_data,
                    'record_count': len(window_data)
                })

            current_time = window_end

        return windows

    def _split_test_data_by_continuous_conditions(self, df: pd.DataFrame) -> List[Dict]:
        """
        测试数据特殊切分：按连续工况的持续时间决定是否切分
        """
        if 'identified_condition' not in df.columns:
            return []

        windows = []

        # 按连续工况分组
        condition_groups = []
        current_condition = None
        current_group = []

        for _, row in df.iterrows():
            if row['identified_condition'] != current_condition:
                if current_group:
                    condition_groups.append({
                        'condition': current_condition,
                        'data': pd.DataFrame(current_group)
                    })
                current_condition = row['identified_condition']
                current_group = [row]
            else:
                current_group.append(row)

        # 添加最后一组
        if current_group:
            condition_groups.append({
                'condition': current_condition,
                'data': pd.DataFrame(current_group)
            })

        # 对每个连续工况组进行处理
        for group in condition_groups:
            group_df = group['data'].reset_index(drop=True)
            condition = group['condition']

            # 计算持续时间
            start_time = group_df['datetime'].min()
            end_time = group_df['datetime'].max()
            duration_minutes = (end_time - start_time).total_seconds() / 60

            if duration_minutes >= self.window_config['test_data_min_duration_minutes']:
                # 超过3分钟，进行切分
                sub_windows = self._split_data_by_fixed_windows(group_df)
                for i, window in enumerate(sub_windows):
                    window['window_name'] = f'{condition}_{window["window_name"]}'
                    windows.extend([window])
            else:
                # 不足3分钟，作为一个整体窗口
                if len(group_df) >= self.window_config['min_records_per_window']:
                    time_range = f"{start_time.strftime('%H:%M:%S')}-{end_time.strftime('%H:%M:%S')}"
                    windows.append({
                        'window_name': f'{condition}_完整片段',
                        'time_range': time_range,
                        'data': group_df,
                        'record_count': len(group_df)
                    })

        return windows

    def classify_data_by_conditions(self, df: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """
        按工况分类数据
        """
        if df.empty or 'identified_condition' not in df.columns:
            return {}

        classified_data = {}

        for condition in self.target_conditions:
            condition_data = df[df['identified_condition'] == condition].copy()
            if not condition_data.empty:
                classified_data[condition] = condition_data

        return classified_data

    def save_raw_data(self, df: pd.DataFrame, event: Dict, data_type: str, start_time: datetime) -> str:
        """
        保存原始数据（未经处理的数据）
        """
        if df.empty:
            return None

        well_name = event['well_name']
        stuck_time = event['stuck_time']

        # 创建原始数据目录
        raw_output_dir = self.output_base_dir / 'raw_data' / self.export_configs[data_type]['folder']
        raw_output_dir.mkdir(parents=True, exist_ok=True)

        # 生成文件名
        if data_type == 'symptom':
            filename = f"{well_name}_{stuck_time.strftime('%Y%m%d_%H%M%S')}_原始征兆数据.csv"
        elif data_type == 'test':
            filename = f"{well_name}_{stuck_time.strftime('%Y%m%d_%H%M%S')}_原始测试数据.csv"
        elif data_type == 'normal':
            filename = f"{well_name}_{stuck_time.strftime('%Y%m%d_%H%M%S')}_原始正常数据_{start_time.strftime('%Y%m%d_%H%M%S')}.csv"

        # 保存原始数据（只保留原始字段，不添加工况识别等列）
        raw_file = raw_output_dir / filename
        df.to_csv(raw_file, index=False, encoding='utf-8-sig')

        print(f"   💾 保存原始数据: {filename} ({len(df)} 条记录)")
        return str(raw_file)

    def clean_output_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        清理输出列，只保留原始数据的列
        """
        if df.empty:
            return df

        # 保留的列：原始数据字段 + date字段
        keep_columns = self.required_fields + ['date']

        # 只保留存在的列
        available_columns = [col for col in keep_columns if col in df.columns]

        return df[available_columns].copy()

    def process_single_event_data(self, event: Dict, data_type: str, sample_hours: int = None) -> Dict:
        """
        处理单个卡钻事件的指定类型数据

        参数:
            event: 卡钻事件信息
            data_type: 数据类型 ('symptom', 'test', 'normal')
            sample_hours: 正常数据的采样时长（小时）
        """
        well_name = event['well_name']
        stuck_time = event['stuck_time']

        # 计算时间范围
        if data_type == 'symptom':
            end_time = stuck_time
            start_time = stuck_time - timedelta(minutes=self.export_configs['symptom']['time_before_minutes'])
        elif data_type == 'test':
            # 测试数据：卡钻前24小时到卡钻前15分钟（避免与征兆数据重合）
            end_time = stuck_time - timedelta(minutes=self.export_configs['symptom']['time_before_minutes'])
            start_time = stuck_time - timedelta(hours=self.export_configs['test']['time_before_hours'])
        elif data_type == 'normal':
            # 正常数据：在卡钻前7-30天范围内随机选择，采样6小时
            if sample_hours is None:
                sample_hours = self.export_configs['normal']['sample_hours']

            range_end = stuck_time - timedelta(days=self.export_configs['normal']['time_before_days_start'])
            range_start = stuck_time - timedelta(days=self.export_configs['normal']['time_before_days_end'])

            import random
            total_seconds = int((range_end - range_start).total_seconds())
            random_seconds = random.randint(0, max(0, total_seconds - sample_hours * 3600))

            start_time = range_start + timedelta(seconds=random_seconds)
            end_time = start_time + timedelta(hours=sample_hours)
        else:
            return {'success': False, 'error': f'不支持的数据类型: {data_type}'}

        print(f"\n📊 处理{self.export_configs[data_type]['name']}: {well_name}")
        print(f"   卡钻时间: {stuck_time}")
        print(f"   数据范围: {start_time} 至 {end_time}")

        # 获取井路径
        well_path = self.base_path / well_name
        if not well_path.exists():
            return {'success': False, 'error': f'井路径不存在: {well_path}'}

        # 获取数据库文件
        db_files = self.get_db_files_for_time_range(well_path, start_time, end_time)
        if not db_files:
            return {'success': False, 'error': '未找到相关数据库文件'}

        # 提取原始数据
        df = self.extract_data_from_db(db_files, start_time, end_time)
        if df.empty:
            return {'success': False, 'error': '未提取到数据'}

        print(f"   📈 提取原始数据: {len(df)} 条记录")

        # 工况识别
        df = self.identify_drilling_condition(df)
        print(f"   🔍 完成工况识别")

        # 按目标工况筛选
        filtered_df = self.filter_by_target_conditions(df)
        print(f"   🎯 筛选目标工况: {len(filtered_df)} 条记录")

        if filtered_df.empty:
            return {'success': False, 'error': '筛选后无有效数据'}

        # 保存原始数据（未经处理）
        self.save_raw_data(df, event, data_type, start_time)

        # 数据质量验证
        quality_report = self.validate_data_quality(filtered_df)

        # 按工况分类数据
        classified_data = self.classify_data_by_conditions(filtered_df)
        print(f"   📊 工况分类: {list(classified_data.keys())}")

        if not classified_data:
            return {'success': False, 'error': '没有找到目标工况的数据'}

        # 保存处理后的数据（按工况/数据类型结构）
        saved_files = []
        total_windows = 0

        # 为每种工况创建目录结构：工况/数据类型
        for condition, condition_data in classified_data.items():
            condition_dir = self.output_base_dir / 'processed_data' / condition / self.export_configs[data_type]['folder']
            condition_dir.mkdir(parents=True, exist_ok=True)

            print(f"   📁 处理工况 '{condition}': {len(condition_data)} 条记录")

            # 进行时间窗口切分
            time_windows = self.split_data_by_time_windows(condition_data, data_type)

            if time_windows:
                # 保存每个时间窗口
                for window_info in time_windows:
                    if data_type == 'symptom':
                        filename = f"{well_name}_{stuck_time.strftime('%Y%m%d_%H%M%S')}_征兆数据_{window_info['window_name']}.csv"
                    elif data_type == 'test':
                        filename = f"{well_name}_{stuck_time.strftime('%Y%m%d_%H%M%S')}_测试数据_{window_info['window_name']}.csv"
                    elif data_type == 'normal':
                        filename = f"{well_name}_{stuck_time.strftime('%Y%m%d_%H%M%S')}_正常数据_{window_info['window_name']}.csv"

                    # 清理输出列，只保留原始数据的列
                    clean_data = self.clean_output_columns(window_info['data'])

                    output_file = condition_dir / filename
                    clean_data.to_csv(output_file, index=False, encoding='utf-8-sig')
                    saved_files.append(str(output_file))
                    total_windows += 1

                print(f"      ⏱️ 创建 {len(time_windows)} 个时间窗口")
            else:
                # 如果无法创建时间窗口，保存原始数据
                if data_type == 'symptom':
                    filename = f"{well_name}_{stuck_time.strftime('%Y%m%d_%H%M%S')}_征兆数据.csv"
                elif data_type == 'test':
                    filename = f"{well_name}_{stuck_time.strftime('%Y%m%d_%H%M%S')}_测试数据.csv"
                elif data_type == 'normal':
                    filename = f"{well_name}_{stuck_time.strftime('%Y%m%d_%H%M%S')}_正常数据_{start_time.strftime('%Y%m%d_%H%M%S')}.csv"

                # 清理输出列，只保留原始数据的列
                clean_data = self.clean_output_columns(condition_data)

                output_file = condition_dir / filename
                clean_data.to_csv(output_file, index=False, encoding='utf-8-sig')
                saved_files.append(str(output_file))
                print(f"      📄 保存完整数据（无法切分时间窗口）")

        result = {
            'success': True,
            'data_type': data_type,
            'well_name': well_name,
            'stuck_time': stuck_time,
            'time_range': f"{start_time} 至 {end_time}",
            'output_files': saved_files,
            'original_record_count': len(df),
            'filtered_record_count': len(filtered_df),
            'conditions_found': list(classified_data.keys()),
            'conditions_count': len(classified_data),
            'time_windows_count': total_windows,
            'quality_report': quality_report
        }

        print(f"   ✅ 数据处理成功: {len(saved_files)} 个文件，{len(classified_data)} 种工况，{total_windows} 个时间窗口")
        print(f"   📈 数据质量分数: {quality_report['quality_score']:.3f}")

        return result

    def batch_process_all_data(self, stuck_events: List[Dict], data_types: List[str] = None) -> Dict:
        """
        批量处理所有卡钻事件的所有类型数据

        参数:
            stuck_events: 卡钻事件列表
            data_types: 要处理的数据类型列表，默认处理所有类型
        """
        if data_types is None:
            data_types = ['symptom', 'test', 'normal']

        print(f"\n🚀 开始批量处理钻井卡钻数据")
        print(f"   卡钻事件数量: {len(stuck_events)}")
        print(f"   处理数据类型: {data_types}")
        print("=" * 60)

        results = {
            'total_events': len(stuck_events),
            'data_types': data_types,
            'results': [],
            'summary': {
                'successful': 0,
                'failed': 0,
                'by_type': {data_type: {'success': 0, 'failed': 0} for data_type in data_types}
            }
        }

        for i, event in enumerate(stuck_events, 1):
            print(f"\n📋 处理事件 {i}/{len(stuck_events)}: {event['well_name']}")
            print(f"   卡钻时间: {event['stuck_time']}")

            event_results = {
                'event': event,
                'results': {},
                'success_count': 0,
                'failed_count': 0
            }

            # 处理每种数据类型
            for data_type in data_types:
                try:
                    result = self.process_single_event_data(event, data_type)
                    event_results['results'][data_type] = result

                    if result['success']:
                        event_results['success_count'] += 1
                        results['summary']['by_type'][data_type]['success'] += 1
                    else:
                        event_results['failed_count'] += 1
                        results['summary']['by_type'][data_type]['failed'] += 1
                        print(f"   ❌ {self.export_configs[data_type]['name']}处理失败: {result.get('error', '未知错误')}")

                except Exception as e:
                    event_results['results'][data_type] = {
                        'success': False,
                        'error': f'处理异常: {str(e)}'
                    }
                    event_results['failed_count'] += 1
                    results['summary']['by_type'][data_type]['failed'] += 1
                    print(f"   ❌ {self.export_configs[data_type]['name']}处理异常: {e}")

            # 更新总体统计
            if event_results['success_count'] > 0:
                results['summary']['successful'] += 1
            if event_results['failed_count'] > 0:
                results['summary']['failed'] += 1

            results['results'].append(event_results)

        return results

    def generate_processing_report(self, results: Dict) -> str:
        """
        生成处理报告
        """
        report_file = self.output_base_dir / "统一处理报告.md"

        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("# 钻井卡钻数据统一处理报告\n\n")
            f.write(f"**处理时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            # 总体统计
            f.write("## 总体统计\n\n")
            f.write(f"- **总事件数**: {results['total_events']}\n")
            f.write(f"- **成功事件数**: {results['summary']['successful']}\n")
            f.write(f"- **失败事件数**: {results['summary']['failed']}\n")
            f.write(f"- **成功率**: {results['summary']['successful']/max(results['total_events'], 1):.2%}\n\n")

            # 按数据类型统计
            f.write("## 按数据类型统计\n\n")
            f.write("| 数据类型 | 成功 | 失败 | 成功率 |\n")
            f.write("|----------|------|------|--------|\n")

            for data_type in results['data_types']:
                type_stats = results['summary']['by_type'][data_type]
                total = type_stats['success'] + type_stats['failed']
                success_rate = type_stats['success'] / max(total, 1)

                f.write(f"| {self.export_configs[data_type]['name']} | {type_stats['success']} | {type_stats['failed']} | {success_rate:.2%} |\n")

            # 详细结果
            f.write("\n## 详细处理结果\n\n")

            for i, event_result in enumerate(results['results'], 1):
                event = event_result['event']
                f.write(f"### {i}. {event['well_name']}\n\n")
                f.write(f"- **卡钻时间**: {event['stuck_time']}\n")
                f.write(f"- **成功处理**: {event_result['success_count']} 种数据类型\n")
                f.write(f"- **失败处理**: {event_result['failed_count']} 种数据类型\n\n")

                # 各数据类型处理结果
                for data_type, result in event_result['results'].items():
                    status = "✅ 成功" if result['success'] else "❌ 失败"
                    f.write(f"- **{self.export_configs[data_type]['name']}**: {status}")

                    if result['success']:
                        f.write(f" - {result.get('filtered_record_count', 0)} 条记录")
                        f.write(f" - {result.get('conditions_count', 0)} 种工况")
                        if 'time_windows_count' in result and result['time_windows_count'] > 0:
                            f.write(f" - {result['time_windows_count']} 个时间窗口")
                        f.write(f" - 质量分数: {result.get('quality_report', {}).get('quality_score', 0):.3f}")
                        if 'conditions_found' in result:
                            f.write(f" - 工况类型: {', '.join(result['conditions_found'])}")
                    else:
                        f.write(f" - 错误: {result.get('error', '未知错误')}")

                    f.write("\n")

                f.write("\n")

            # 智能工况识别配置
            f.write("## 智能工况识别配置\n\n")
            f.write("### 算法特性\n\n")
            f.write("- **识别模式**: 混合算法（无监督聚类 + 规则验证）\n")
            f.write("- **多尺度分析**: 30秒、2分钟、5分钟时间窗口\n")
            f.write("- **参数融合**: DEP、BITDEP、RPM、WOB、TOR、SPP等多参数\n")
            f.write("- **动态阈值**: 基于井深和数据特征自适应调整\n")
            f.write("- **置信度评估**: 基于聚类距离和工况稳定性\n\n")

            f.write("### 时间窗口配置\n\n")
            f.write(f"- **短期窗口**: {self.smart_recognizer.time_windows['short']} 个数据点 (约30秒)\n")
            f.write(f"- **中期窗口**: {self.smart_recognizer.time_windows['medium']} 个数据点 (约2分钟)\n")
            f.write(f"- **长期窗口**: {self.smart_recognizer.time_windows['long']} 个数据点 (约5分钟)\n\n")

            f.write("### 参数权重配置\n\n")
            for param, weight in self.smart_recognizer.parameter_weights.items():
                f.write(f"- **{param}**: {weight:.2f}\n")
            f.write("\n")

            f.write("### 聚类配置\n\n")
            f.write(f"- **聚类数量**: {self.smart_recognizer.clustering_config['n_clusters']}\n")
            f.write(f"- **最大迭代**: {self.smart_recognizer.clustering_config['max_iter']}\n")
            f.write(f"- **初始化次数**: {self.smart_recognizer.clustering_config['n_init']}\n\n")

            f.write("### 目标工况\n\n")
            for condition in self.target_conditions:
                f.write(f"- {condition}\n")

            f.write("\n### 时间窗口切分配置\n\n")
            f.write(f"- **窗口长度**: {self.window_config['window_duration_minutes']} 分钟\n")
            f.write(f"- **最少记录数**: {self.window_config['min_records_per_window']} 条/窗口\n")
            f.write(f"- **测试数据切分阈值**: {self.window_config['test_data_min_duration_minutes']} 分钟\n")
            f.write(f"- **正常数据采样时长**: {self.export_configs['normal']['sample_hours']} 小时\n\n")

            f.write("### 数据处理策略\n\n")
            f.write("- **征兆数据**: 卡钻前15分钟，按工况分类 + 固定3分钟窗口切分\n")
            f.write("- **测试数据**: 卡钻前24小时到15分钟，按工况分类 + 连续工况时间切分（≥3分钟才切分）\n")
            f.write("- **正常数据**: 卡钻前7-30天随机6小时，按工况分类 + 固定3分钟窗口切分\n")
            f.write("- **原始数据**: 同时保存未经处理的原始数据\n")
            f.write("- **文件结构**: 工况/数据类型/具体文件\n")
            f.write("- **列清理**: 处理后数据只保留原始数据列\n")

        print(f"📋 处理报告已生成: {report_file}")
        return str(report_file)


def main():
    """
    主函数 - 统一钻井卡钻数据处理（优化版本）
    """
    # 配置参数
    BASE_PATH = r"D:\采数据\PROCESS"  # 数据库文件的根目录
    OUTPUT_DIR = r"D:\采数据\PROCESS\unified_processed_data38"  # 输出目录
    STUCK_EVENTS_CSV = "卡钻事件表.csv"  # 卡钻事件CSV文件
    SAMPLING_FREQUENCY = 0.5  # 数据采样频率 (Hz)，默认2秒一个点

    print("🚀 统一钻井卡钻数据处理器 - 智能优化版本")
    print("=" * 60)
    print("功能：数据提取 + 智能工况识别 + 数据筛选 + 时间窗口切分")
    print("新特性：多尺度分析 + 无监督聚类 + 动态阈值 + 置信度评估")
    print("=" * 60)

    # 初始化处理器（支持采样频率配置）
    processor = UnifiedDrillingDataProcessor(BASE_PATH, OUTPUT_DIR, SAMPLING_FREQUENCY)

    # 加载卡钻事件
    stuck_events = processor.load_stuck_events_from_csv(STUCK_EVENTS_CSV)

    if not stuck_events:
        print("❌ 未找到卡钻事件数据，请检查CSV文件")
        print(f"   期望文件: {STUCK_EVENTS_CSV}")
        print("   CSV格式要求:")
        print("   - 井名: 井的名称")
        print("   - 卡钻时间: 卡钻发生的时间 (YYYY-MM-DD HH:MM:SS)")
        print("   - 事件描述: 可选，事件的描述信息")
        return

    # 批量处理所有数据
    try:
        print(f"\n🔄 开始统一处理流程...")
        results = processor.batch_process_all_data(stuck_events)

        # 生成处理报告
        report_file = processor.generate_processing_report(results)

        # 输出总结
        print(f"\n🎉 统一数据处理完成!")
        print("=" * 50)
        print(f"📊 总体统计:")
        print(f"   - 处理事件: {results['total_events']} 个")
        print(f"   - 成功事件: {results['summary']['successful']} 个")
        print(f"   - 失败事件: {results['summary']['failed']} 个")
        print(f"   - 成功率: {results['summary']['successful']/max(results['total_events'], 1):.2%}")

        print(f"\n📈 按数据类型统计:")
        for data_type in results['data_types']:
            type_stats = results['summary']['by_type'][data_type]
            total = type_stats['success'] + type_stats['failed']
            success_rate = type_stats['success'] / max(total, 1)
            print(f"   - {processor.export_configs[data_type]['name']}: {type_stats['success']}/{total} ({success_rate:.2%})")

        print(f"\n📁 输出目录: {OUTPUT_DIR}")
        print(f"📋 详细报告: {report_file}")

        print(f"\n✨ 处理优势:")
        print(f"   - 一次执行完成完整流程")
        print(f"   - 自动工况识别和按工况分类保存")
        print(f"   - 全部数据类型时间窗口切分")
        print(f"   - 测试数据智能连续工况切分")
        print(f"   - 正常数据采样时长优化为6小时")
        print(f"   - 测试数据与征兆数据时间不重合")
        print(f"   - 同时输出原始数据和处理后数据")
        print(f"   - 优化的文件夹结构（工况/数据类型）")
        print(f"   - 无中间文件依赖")

    except Exception as e:
        print(f"❌ 处理过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
