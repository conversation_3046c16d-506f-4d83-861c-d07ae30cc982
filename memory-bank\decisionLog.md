# Decision Log

This file records architectural and implementation decisions using a list format.
2025-07-22 16:33:21 - Log of updates made.

*

## Decision

### [2025-07-23] - 钻井卡钻数据处理脚本合并
将drilling_stuck_data_exporter.py和drilling_data_postprocessor.py合并为unified_drilling_data_processor.py

## Rationale

* **操作简化**: 从两步操作（导出→后处理）简化为一步操作，提升用户体验
* **效率提升**: 消除中间CSV文件依赖，直接在内存中完成数据流转，减少磁盘I/O
* **架构优化**: 统一的数据处理流程，更清晰的代码组织和维护
* **功能完整**: 保持原有所有功能，包括工况识别、数据筛选、时间窗口切分

## Implementation Details

* **统一类设计**: 创建UnifiedDrillingDataProcessor类整合两个脚本功能
* **数据流优化**: 数据提取→工况识别→筛选→切分→输出的一体化流程
* **配置保持**: 保留原有的参数配置和阈值设置，确保处理结果一致性
* **报告增强**: 生成更详细的统一处理报告，包含各阶段统计信息

### [2025-07-22] - 钻井卡钻数据处理流程重构
将工况判断逻辑从数据导出阶段移至后处理阶段，使用基于钻井参数的自动识别算法

## Rationale

* **准确性提升**: 基于实时钻井参数(DEP、BITDEP、RPM)的工况识别比依赖RIGSTA字段更准确
* **架构清晰**: 数据导出和工况判断职责分离，系统架构更清晰
* **可维护性**: 工况识别算法可独立优化和调整，便于后续改进
* **扩展性**: 为引入机器学习算法进行工况识别奠定基础

## Implementation Details

* **导出器修改**: 移除RIGSTA字段依赖，专注于核心钻井参数提取
* **后处理器增强**: 添加identify_drilling_condition()方法实现参数化工况识别
* **识别规则**: 基于井深变化、转速、钻头位置变化的组合判断5种核心工况
* **兼容性**: 保持现有数据格式和API接口不变